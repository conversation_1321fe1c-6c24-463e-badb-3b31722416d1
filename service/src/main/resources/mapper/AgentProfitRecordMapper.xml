<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeequan.jeepay.service.mapper.AgentProfitRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jeequan.jeepay.core.entity.AgentProfitRecord">
        <id column="id" property="id" />
        <result column="agent_no" property="agentNo" />
        <result column="mch_no" property="mchNo" />
        <result column="pay_order_id" property="payOrderId" />
        <result column="order_amount" property="orderAmount" />
        <result column="mch_fee_amount" property="mchFeeAmount" />
        <result column="profit_rate" property="profitRate" />
        <result column="profit_amount" property="profitAmount" />
        <result column="profit_date" property="profitDate" />
        <result column="state" property="state" />
        <result column="settle_time" property="settleTime" />
        <result column="remark" property="remark" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, agent_no, mch_no, pay_order_id, order_amount, mch_fee_amount, profit_rate, profit_amount,
        profit_date, state, settle_time, remark, created_at, updated_at
    </sql>

    <!-- 分页查询代理商分润记录 -->
    <select id="selectProfitRecordPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_agent_profit_record
        WHERE 1=1
        <if test="agentNo != null and agentNo != ''">
            AND agent_no = #{agentNo}
        </if>
        <if test="mchNo != null and mchNo != ''">
            AND mch_no LIKE CONCAT('%', #{mchNo}, '%')
        </if>
        <if test="state != null">
            AND state = #{state}
        </if>
        <if test="startDate != null">
            AND profit_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND profit_date &lt;= #{endDate}
        </if>
        ORDER BY created_at DESC
    </select>

    <!-- 统计代理商分润金额 -->
    <select id="sumProfitAmount" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalCount,
            COALESCE(SUM(profit_amount), 0) as totalProfitAmount,
            COALESCE(SUM(order_amount), 0) as totalOrderAmount,
            COALESCE(SUM(mch_fee_amount), 0) as totalMchFeeAmount
        FROM t_agent_profit_record
        WHERE 1=1
        <if test="agentNo != null and agentNo != ''">
            AND agent_no = #{agentNo}
        </if>
        <if test="state != null">
            AND state = #{state}
        </if>
        <if test="startDate != null">
            AND profit_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND profit_date &lt;= #{endDate}
        </if>
    </select>

    <!-- 批量插入分润记录 -->
    <insert id="batchInsert">
        INSERT INTO t_agent_profit_record (agent_no, mch_no, pay_order_id, order_amount, mch_fee_amount, 
                                          profit_rate, profit_amount, profit_date, state, created_at, updated_at)
        VALUES
        <foreach collection="records" item="item" separator=",">
            (#{item.agentNo}, #{item.mchNo}, #{item.payOrderId}, #{item.orderAmount}, #{item.mchFeeAmount},
             #{item.profitRate}, #{item.profitAmount}, #{item.profitDate}, #{item.state}, #{item.createdAt}, #{item.updatedAt})
        </foreach>
    </insert>

    <!-- 批量更新分润状态 -->
    <update id="batchUpdateState">
        UPDATE t_agent_profit_record
        SET state = #{state}
        <if test="settleTime != null">
            , settle_time = #{settleTime}
        </if>
        , updated_at = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
