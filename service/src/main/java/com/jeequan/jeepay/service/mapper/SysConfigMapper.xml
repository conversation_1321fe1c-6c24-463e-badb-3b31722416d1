<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeequan.jeepay.service.mapper.SysConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jeequan.jeepay.core.entity.SysConfig">
        <id column="config_key" property="configKey" />
        <result column="config_name" property="configName" />
        <result column="config_desc" property="configDesc" />
        <result column="group_key" property="groupKey" />
        <result column="group_name" property="groupName" />
        <result column="config_val" property="configVal" />
        <result column="type" property="type" />
        <result column="sort_num" property="sortNum" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

</mapper>
