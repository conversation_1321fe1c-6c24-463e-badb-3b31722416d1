<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion> <!-- POM模型版本 -->

  <artifactId>components-mq</artifactId>  <!-- 项目名称 -->
  <packaging>jar</packaging> <!-- 项目的最终打包类型/发布形式, 可选[jar, war, pom, maven-plugin]等 -->
  <version>${isys.version}</version> <!-- 项目当前版本号 -->
  <description>四方支付系统 [components-mq]</description> <!-- 项目描述 -->

  <parent>
    <groupId>com.platform.payment</groupId>
    <artifactId>components</artifactId>
    <version>Final</version>
  </parent>

  <!-- 项目依赖声明 -->
  <dependencies>

    <!-- 依赖Core项目  -->
    <dependency>
      <groupId>com.platform.payment</groupId>
      <artifactId>core</artifactId>
    </dependency>

    <!-- ↓↓↓↓↓↓ MQ依赖包 ↓↓↓↓↓↓ -->
    <!-- 使用的厂商 scope=compile,  否则需要scope=provided(仅编译，不依赖) -->

    <!-- ActiveMQ  和 连接池  -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-activemq</artifactId>
<!--      <scope>provided</scope>-->
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>activemq-pool</artifactId>
<!--      <scope>provided</scope>-->
    </dependency>
    <dependency>
      <groupId>org.messaginghub</groupId>
      <artifactId>pooled-jms</artifactId>
<!--      <scope>provided</scope>-->
    </dependency>

    <!-- RabbitMQ  -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-amqp</artifactId>
      <scope>provided</scope>
    </dependency>

    <!-- RocketMQ -->
    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-spring-boot-starter</artifactId>
      <scope>provided</scope>
    </dependency>

    <!-- AliyunRocketMQ -->
    <dependency>
      <groupId>com.aliyun.openservices</groupId>
      <artifactId>ons-client</artifactId>
      <scope>provided</scope>
    </dependency>

    <!-- ↑↑↑↑↑↑ MQ依赖包 ↑↑↑↑↑↑ -->

  </dependencies>

  <build>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
      </resource>
      <resource>
        <directory>src/main/java</directory>
        <includes><include>**/*.xml</include></includes><!-- maven可以将mapper.xml进行打包处理，否则仅对java文件处理 -->
      </resource>
    </resources>

  </build>

</project>
