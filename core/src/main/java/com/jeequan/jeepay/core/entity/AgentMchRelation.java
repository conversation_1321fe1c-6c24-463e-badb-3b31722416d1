/*
 * Copyright (c) 2021-2031, 河北计全科技有限公司 (https://www.jeequan.com & <EMAIL>).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.jeequan.jeepay.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jeequan.jeepay.core.model.BaseModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 代理商商户关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Schema(description = "代理商商户关系表")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_agent_mch_relation")
public class AgentMchRelation extends BaseModel implements Serializable {

    public static final LambdaQueryWrapper<AgentMchRelation> gw(){
        return new LambdaQueryWrapper<>();
    }

    private static final long serialVersionUID = 1L;

    // 关系类型
    public static final byte RELATION_TYPE_DIRECT = 1; //直属商户
    public static final byte RELATION_TYPE_INDIRECT = 2; //下级代理商的商户

    // 关系状态
    public static final byte STATE_STOP = 0; //停用
    public static final byte STATE_NORMAL = 1; //正常

    /**
     * ID
     */
    @Schema(title = "id", description = "ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 代理商号
     */
    @Schema(title = "agentNo", description = "代理商号")
    private String agentNo;

    /**
     * 商户号
     */
    @Schema(title = "mchNo", description = "商户号")
    private String mchNo;

    /**
     * 关系类型: 1-直属商户, 2-下级代理商的商户
     */
    @Schema(title = "relationType", description = "关系类型: 1-直属商户, 2-下级代理商的商户")
    private Byte relationType;

    /**
     * 该商户给代理商的分润比例
     */
    @Schema(title = "profitRate", description = "该商户给代理商的分润比例")
    private BigDecimal profitRate;

    /**
     * 关系状态: 0-停用, 1-正常
     */
    @Schema(title = "state", description = "关系状态: 0-停用, 1-正常")
    private Byte state;

    /**
     * 创建者用户ID
     */
    @Schema(title = "createdUid", description = "创建者用户ID")
    private Long createdUid;

    /**
     * 创建者姓名
     */
    @Schema(title = "createdBy", description = "创建者姓名")
    private String createdBy;

    /**
     * 创建时间
     */
    @Schema(title = "createdAt", description = "创建时间")
    private Date createdAt;

    /**
     * 更新时间
     */
    @Schema(title = "updatedAt", description = "更新时间")
    private Date updatedAt;

}
