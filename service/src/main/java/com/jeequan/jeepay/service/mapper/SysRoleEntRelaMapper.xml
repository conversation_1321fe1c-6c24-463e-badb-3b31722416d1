<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeequan.jeepay.service.mapper.SysRoleEntRelaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jeequan.jeepay.core.entity.SysRoleEntRela">
        <id column="role_id" property="roleId" />
        <result column="ent_id" property="entId" />
    </resultMap>

    <!-- 根据用户查询所有的entId集合  -->
    <select id="selectEntIdsByUserId" resultType="String">
        select distinct ent_id from t_sys_entitlement where ent_id in (
            select distinct re.ent_id from t_sys_role_ent_rela re left join t_sys_user_role_rela ur on re.role_id = ur.role_id
            where ur.user_id = #{userId}
        ) and `sys_type` = #{sysType} and state = 1
    </select>

</mapper>
