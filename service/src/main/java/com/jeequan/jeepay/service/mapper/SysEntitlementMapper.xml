<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeequan.jeepay.service.mapper.SysEntitlementMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jeequan.jeepay.core.entity.SysEntitlement">
        <id column="ent_id" property="entId" />
        <result column="ent_name" property="entName" />
        <result column="menu_icon" property="menuIcon" />
        <result column="menu_uri" property="menuUri" />
        <result column="component_name" property="componentName" />
        <result column="ent_type" property="entType" />
        <result column="quick_jump" property="quickJump" />
        <result column="state" property="state" />
        <result column="pid" property="pid" />
        <result column="ent_sort" property="entSort" />
        <result column="sys_type" property="sysType" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 查询当前用户是否存在左侧菜单 (仅普通操作员) -->
    <select id="userHasLeftMenu" resultType="Integer">
    select count(1) from t_sys_entitlement where ent_id in (
        select DISTINCT ent_id from t_sys_role_ent_rela where role_id in (
            select role_id from  t_sys_user_role_rela where user_id = #{userId}
        )
    ) and state = 1 and ent_type = 'ML' and sys_type  = #{sysType}
</select>




</mapper>
