/*
 * Copyright (c) 2021-2031, 河北计全科技有限公司 (https://www.jeequan.com & <EMAIL>).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.jeequan.jeepay.agent.ctrl.agent;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jeequan.jeepay.core.aop.MethodLog;
import com.jeequan.jeepay.core.constants.ApiCodeEnum;
import com.jeequan.jeepay.core.constants.CS;
import com.jeequan.jeepay.core.entity.AgentInfo;
import com.jeequan.jeepay.core.entity.SysUser;
import com.jeequan.jeepay.core.exception.BizException;
import com.jeequan.jeepay.core.model.ApiPageRes;
import com.jeequan.jeepay.core.model.ApiRes;
import com.jeequan.jeepay.core.utils.StringKit;
import com.jeequan.jeepay.service.impl.AgentInfoService;
import com.jeequan.jeepay.service.impl.SysUserService;
import com.jeequan.jeepay.agent.ctrl.CommonCtrl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 代理商信息管理类
 *
 * <AUTHOR>
 * @site https://www.jeequan.com
 * @date 2024-01-01
 */
@Tag(name = "代理商管理")
@RestController
@RequestMapping("/api/agentInfo")
public class AgentInfoController extends CommonCtrl {

    @Autowired private AgentInfoService agentInfoService;
    @Autowired private SysUserService sysUserService;

    /**
     * <AUTHOR>
     * @site https://www.jeequan.com
     * @date 2024-01-01
     */
    @Operation(summary = "代理商信息列表")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "pageNumber", description = "分页页码"),
            @Parameter(name = "pageSize", description = "分页条数（-1时查全部数据）"),
            @Parameter(name = "agentNo", description = "代理商号"),
            @Parameter(name = "agentName", description = "代理商名称"),
            @Parameter(name = "state", description = "状态: 0-停用, 1-正常")
    })
    @PreAuthorize("hasAuthority('ENT_AGENT_LIST')")
    @GetMapping
    public ApiPageRes<AgentInfo> list() {

        AgentInfo queryObject = getObject(AgentInfo.class);
        
        // 获取当前登录用户的代理商号，只能查看自己的下级代理商
        String currentAgentNo = getCurrentUser().getSysUser().getBelongInfoId();

        IPage<AgentInfo> pages = agentInfoService.selectPage(getIPage(), queryObject, currentAgentNo);

        return ApiPageRes.pages(pages);
    }

    /**
     * <AUTHOR>
     * @site https://www.jeequan.com
     * @date 2024-01-01
     */
    @Operation(summary = "代理商详情")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "agentNo", description = "代理商号", required = true)
    })
    @PreAuthorize("hasAuthority('ENT_AGENT_INFO_VIEW')")
    @GetMapping("/{agentNo}")
    public ApiRes<AgentInfo> detail(@PathVariable("agentNo") String agentNo) {
        AgentInfo agentInfo = agentInfoService.getByAgentNo(agentNo);
        if (agentInfo == null) {
            return ApiRes.fail(ApiCodeEnum.SYS_OPERATION_FAIL_SELETE, "该代理商不存在");
        }
        return ApiRes.ok(agentInfo);
    }

    /**
     * <AUTHOR>
     * @site https://www.jeequan.com
     * @date 2024-01-01
     */
    @Operation(summary = "新增代理商")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER)
    })
    @PreAuthorize("hasAuthority('ENT_AGENT_INFO_ADD')")
    @MethodLog(remark = "新增代理商")
    @PostMapping
    public ApiRes<AgentInfo> add() {
        AgentInfo agentInfo = getObject(AgentInfo.class);

        // 校验代理商号是否重复
        if (agentInfoService.isExistAgentNo(agentInfo.getAgentNo())) {
            return ApiRes.fail(ApiCodeEnum.SYS_OPERATION_FAIL_CREATE, "代理商号已存在");
        }

        // 设置上级代理商为当前登录用户的代理商
        String currentAgentNo = getCurrentUser().getSysUser().getBelongInfoId();
        agentInfo.setParentAgentNo(currentAgentNo);

        // 设置创建信息
        SysUser currentUser = getCurrentUser().getSysUser();
        agentInfo.setCreatedUid(currentUser.getSysUserId());
        agentInfo.setCreatedBy(currentUser.getRealname());
        agentInfo.setCreatedAt(new Date());
        agentInfo.setUpdatedAt(new Date());

        // 默认状态为正常
        if (agentInfo.getState() == null) {
            agentInfo.setState(AgentInfo.STATE_NORMAL);
        }

        boolean result = agentInfoService.createAgent(agentInfo);
        if (!result) {
            return ApiRes.fail(ApiCodeEnum.SYS_OPERATION_FAIL_CREATE, "新增失败");
        }
        return ApiRes.ok(agentInfo);
    }

    /**
     * <AUTHOR>
     * @site https://www.jeequan.com
     * @date 2024-01-01
     */
    @Operation(summary = "更新代理商信息")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "agentNo", description = "代理商号", required = true)
    })
    @PreAuthorize("hasAuthority('ENT_AGENT_INFO_EDIT')")
    @MethodLog(remark = "更新代理商信息")
    @PutMapping("/{agentNo}")
    public ApiRes<AgentInfo> update(@PathVariable("agentNo") String agentNo) {
        AgentInfo agentInfo = getObject(AgentInfo.class);
        agentInfo.setAgentNo(agentNo);
        agentInfo.setUpdatedAt(new Date());

        boolean result = agentInfoService.updateAgent(agentInfo);
        if (!result) {
            return ApiRes.fail(ApiCodeEnum.SYS_OPERATION_FAIL_UPDATE, "更新失败");
        }
        return ApiRes.ok(agentInfo);
    }

    /**
     * <AUTHOR>
     * @site https://www.jeequan.com
     * @date 2024-01-01
     */
    @Operation(summary = "删除代理商")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "agentNo", description = "代理商号", required = true)
    })
    @PreAuthorize("hasAuthority('ENT_AGENT_INFO_DELETE')")
    @MethodLog(remark = "删除代理商")
    @DeleteMapping("/{agentNo}")
    public ApiRes<?> delete(@PathVariable("agentNo") String agentNo) {
        
        // 检查是否可以删除
        if (!agentInfoService.canDeleteAgent(agentNo)) {
            return ApiRes.fail(ApiCodeEnum.SYS_OPERATION_FAIL_DELETE, "该代理商下存在下级代理商或关联商户，无法删除");
        }

        boolean result = agentInfoService.removeById(agentNo);
        if (!result) {
            return ApiRes.fail(ApiCodeEnum.SYS_OPERATION_FAIL_DELETE, "删除失败");
        }
        return ApiRes.ok();
    }

}
