/*
 * Copyright (c) 2021-2031, 河北计全科技有限公司 (https://www.jeequan.com & <EMAIL>).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.jeequan.jeepay.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeequan.jeepay.core.entity.AgentInfo;
import com.jeequan.jeepay.service.mapper.AgentInfoMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 代理商信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class AgentInfoService extends ServiceImpl<AgentInfoMapper, AgentInfo> {

    /**
     * 根据代理商号查询代理商信息
     * @param agentNo 代理商号
     * @return 代理商信息
     */
    public AgentInfo getByAgentNo(String agentNo) {
        return getOne(AgentInfo.gw().eq(AgentInfo::getAgentNo, agentNo));
    }

    /**
     * 分页查询代理商列表
     * @param page 分页参数
     * @param agentInfo 查询条件
     * @param parentAgentNo 上级代理商号（用于权限控制）
     * @return 代理商分页数据
     */
    public IPage<AgentInfo> selectPage(IPage<AgentInfo> page, AgentInfo agentInfo, String parentAgentNo) {
        LambdaQueryWrapper<AgentInfo> wrapper = AgentInfo.gw();
        
        // 如果指定了上级代理商，只查询其下级代理商
        if (StringUtils.isNotBlank(parentAgentNo)) {
            wrapper.eq(AgentInfo::getParentAgentNo, parentAgentNo);
        }
        
        if (agentInfo != null) {
            if (StringUtils.isNotBlank(agentInfo.getAgentNo())) {
                wrapper.like(AgentInfo::getAgentNo, agentInfo.getAgentNo());
            }
            if (StringUtils.isNotBlank(agentInfo.getAgentName())) {
                wrapper.like(AgentInfo::getAgentName, agentInfo.getAgentName());
            }
            if (agentInfo.getState() != null) {
                wrapper.eq(AgentInfo::getState, agentInfo.getState());
            }
            if (agentInfo.getAgentLevel() != null) {
                wrapper.eq(AgentInfo::getAgentLevel, agentInfo.getAgentLevel());
            }
        }
        
        wrapper.orderByDesc(AgentInfo::getCreatedAt);
        return page(page, wrapper);
    }

    /**
     * 根据上级代理商号查询直属下级代理商
     * @param parentAgentNo 上级代理商号
     * @return 直属下级代理商列表
     */
    public List<AgentInfo> getDirectSubAgents(String parentAgentNo) {
        return baseMapper.selectDirectSubAgents(parentAgentNo);
    }

    /**
     * 根据代理商路径查询所有下级代理商
     * @param agentPath 代理商路径
     * @return 所有下级代理商列表
     */
    public List<AgentInfo> getAllSubAgents(String agentPath) {
        return baseMapper.selectSubAgentsByPath(agentPath + "%");
    }

    /**
     * 创建代理商
     * @param agentInfo 代理商信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createAgent(AgentInfo agentInfo) {
        // 构建代理商路径
        String agentPath = buildAgentPath(agentInfo.getParentAgentNo(), agentInfo.getAgentNo());
        agentInfo.setAgentPath(agentPath);
        
        // 设置代理商层级
        if (StringUtils.isBlank(agentInfo.getParentAgentNo())) {
            agentInfo.setAgentLevel((byte) 1);
        } else {
            AgentInfo parentAgent = getByAgentNo(agentInfo.getParentAgentNo());
            if (parentAgent != null) {
                agentInfo.setAgentLevel((byte) (parentAgent.getAgentLevel() + 1));
            }
        }
        
        return save(agentInfo);
    }

    /**
     * 更新代理商信息
     * @param agentInfo 代理商信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAgent(AgentInfo agentInfo) {
        // 如果修改了上级代理商，需要重新构建路径
        AgentInfo oldAgent = getByAgentNo(agentInfo.getAgentNo());
        if (oldAgent != null && !StringUtils.equals(oldAgent.getParentAgentNo(), agentInfo.getParentAgentNo())) {
            String newAgentPath = buildAgentPath(agentInfo.getParentAgentNo(), agentInfo.getAgentNo());
            agentInfo.setAgentPath(newAgentPath);
            
            // 更新层级
            if (StringUtils.isBlank(agentInfo.getParentAgentNo())) {
                agentInfo.setAgentLevel((byte) 1);
            } else {
                AgentInfo parentAgent = getByAgentNo(agentInfo.getParentAgentNo());
                if (parentAgent != null) {
                    agentInfo.setAgentLevel((byte) (parentAgent.getAgentLevel() + 1));
                }
            }
        }
        
        return updateById(agentInfo);
    }

    /**
     * 构建代理商路径
     * @param parentAgentNo 上级代理商号
     * @param agentNo 代理商号
     * @return 代理商路径
     */
    private String buildAgentPath(String parentAgentNo, String agentNo) {
        if (StringUtils.isBlank(parentAgentNo)) {
            return "/" + agentNo;
        }
        
        AgentInfo parentAgent = getByAgentNo(parentAgentNo);
        if (parentAgent == null) {
            return "/" + agentNo;
        }
        
        return parentAgent.getAgentPath() + "/" + agentNo;
    }

    /**
     * 检查代理商号是否存在
     * @param agentNo 代理商号
     * @return 是否存在
     */
    public boolean isExistAgentNo(String agentNo) {
        return count(AgentInfo.gw().eq(AgentInfo::getAgentNo, agentNo)) > 0;
    }

    /**
     * 检查是否可以删除代理商（没有下级代理商和关联商户）
     * @param agentNo 代理商号
     * @return 是否可以删除
     */
    public boolean canDeleteAgent(String agentNo) {
        // 检查是否有下级代理商
        long subAgentCount = count(AgentInfo.gw().eq(AgentInfo::getParentAgentNo, agentNo));
        return subAgentCount == 0;
    }

}
