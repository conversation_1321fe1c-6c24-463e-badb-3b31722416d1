server:
  port: 9219 #设置端口

spring:
  data:
    redis:
      database: 4    #1库：运营平台  #2库：商户系统 #3库：支付网关 #4库：代理商系统

#系统业务参数
isys:
    jwt-secret: ARNXp4MzjOOQqxtv #生成jwt的秘钥。 要求每个系统有单独的秘钥管理机制。

# knife4j APIDOC文档
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      packages-to-scan: com.unipay.agent.ctrl
knife4j:
  enable: true
