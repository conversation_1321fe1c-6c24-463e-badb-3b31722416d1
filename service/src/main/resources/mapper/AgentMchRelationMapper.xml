<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeequan.jeepay.service.mapper.AgentMchRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jeequan.jeepay.core.entity.AgentMchRelation">
        <id column="id" property="id" />
        <result column="agent_no" property="agentNo" />
        <result column="mch_no" property="mchNo" />
        <result column="relation_type" property="relationType" />
        <result column="profit_rate" property="profitRate" />
        <result column="state" property="state" />
        <result column="created_uid" property="createdUid" />
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, agent_no, mch_no, relation_type, profit_rate, state, created_uid, created_by, created_at, updated_at
    </sql>

    <!-- 根据代理商号查询所有关联商户 -->
    <select id="selectMchNosByAgentNo" resultType="java.lang.String">
        SELECT mch_no
        FROM t_agent_mch_relation
        WHERE agent_no = #{agentNo}
        AND state = 1
    </select>

    <!-- 根据商户号查询所有关联代理商 -->
    <select id="selectAgentNosByMchNo" resultType="java.lang.String">
        SELECT agent_no
        FROM t_agent_mch_relation
        WHERE mch_no = #{mchNo}
        AND state = 1
    </select>

    <!-- 批量插入代理商商户关系 -->
    <insert id="batchInsert">
        INSERT INTO t_agent_mch_relation (agent_no, mch_no, relation_type, profit_rate, state, created_uid, created_by, created_at, updated_at)
        VALUES
        <foreach collection="relations" item="item" separator=",">
            (#{item.agentNo}, #{item.mchNo}, #{item.relationType}, #{item.profitRate}, #{item.state}, 
             #{item.createdUid}, #{item.createdBy}, #{item.createdAt}, #{item.updatedAt})
        </foreach>
    </insert>

</mapper>
