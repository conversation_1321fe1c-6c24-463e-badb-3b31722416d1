/*
 * Copyright (c) 2021-2031, 河北计全科技有限公司 (https://www.jeequan.com & <EMAIL>).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.jeequan.jeepay.components.mq.constant;

/**
* MQ 厂商定义类
*
* <AUTHOR>
* @site https://www.jeequan.com
* @date 2021/7/23 16:50
*/
public class MQVenderCS {

    public static final String YML_VENDER_KEY = "isys.mq.vender";

    public static final String ACTIVE_MQ = "activeMQ";
    public static final String RABBIT_MQ = "rabbitMQ";
    public static final String ROCKET_MQ = "rocketMQ";
    public static final String ALIYUN_ROCKET_MQ = "aliYunRocketMQ";

}
